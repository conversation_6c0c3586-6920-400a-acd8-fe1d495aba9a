# test.py 代码整理说明

## 整理概述

本次对 `test.py` 程序进行了全面的整理和优化，主要目标是：
1. 改善代码结构和可读性
2. 添加缓存清除和内存管理功能
3. 增强程序的性能监控能力
4. 优化注释和文档

## 主要改进内容

### 1. 导入模块优化
- **添加**: `gc` (垃圾回收) 和 `psutil` (进程监控) 模块
- **移除**: 未使用的 `random` 模块
- **目的**: 支持内存管理和性能监控功能

### 2. 新增辅助函数

#### `get_memory_usage()` 函数
```python
def get_memory_usage() -> float:
    """获取当前进程的内存使用量（MB）"""
```
- 功能：实时监控程序内存使用情况
- 返回：当前进程内存使用量（MB）
- 异常处理：如果获取失败返回 0.0

#### `clear_cache_and_memory()` 函数（增强版）
```python
def clear_cache_and_memory(G: nx.Graph, verbose: bool = True) -> None:
    """清除缓存和释放内存"""
```
- **新增功能**：
  - 内存使用前后对比
  - 详细的清理过程报告
  - 释放内存量统计
- **清理内容**：
  - 图对象的邻接列表缓存 (`_neighbors_cache`)
  - 其他图缓存属性 (`_node_cache`, `_edge_cache` 等)
  - 强制垃圾回收

### 3. 算法执行流程优化

#### NM函数内的缓存清除
在 `NM()` 函数结束时添加：
- 清除算法执行过程中的缓存
- 强制垃圾回收
- 详细的清理日志输出

#### 主函数结构化改进
将 `main()` 函数分为5个清晰的阶段：

1. **第一阶段：离散Nelder-Mead算法优化**
2. **第二阶段：局部搜索优化**  
3. **第三阶段：蒙特卡洛IC模型评估**
4. **第四阶段：生成可视化结果**
5. **第五阶段：清理缓存和性能统计**

### 4. 性能监控增强

#### 程序启动监控
```python
initial_memory = get_memory_usage()
print(f"程序启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print(f"初始内存使用: {initial_memory:.2f} MB")
```

#### 程序结束统计
```python
print("程序执行完成 - 性能统计")
print(f"总运行时间: {runtime:.2f} 秒")
print(f"净内存使用: {memory_usage:.2f} MB")
```

### 5. 注释和文档改进

#### 函数文档字符串
- 为 `main()` 函数添加了详细的执行流程说明
- 为新增函数添加了完整的参数和返回值说明

#### 代码分段注释
使用统一的分段标记格式：
```python
# ==================== 段落标题 ====================
```

#### 参数配置说明
- 将算法参数分为"推荐配置"和"备选配置"
- 添加了详细的参数说明注释

### 6. 内存管理策略

#### 三级缓存清理
1. **算法执行中**: 在NM函数结束时清理算法缓存
2. **阶段性清理**: 在主要计算阶段之间进行清理
3. **最终清理**: 程序结束前的全面清理

#### 垃圾回收优化
- 在关键节点强制执行垃圾回收
- 统计回收的对象数量
- 监控内存释放效果

## 使用建议

### 1. 内存监控
程序现在会自动显示：
- 启动时的内存使用
- 各阶段的内存变化
- 最终的内存统计

### 2. 参数调整
可以根据需要在以下位置调整参数：
- 第492-502行：算法基本参数
- 第504-513行：Nelder-Mead系数

### 3. 网络数据
在第495-500行选择要处理的网络数据文件

### 4. 结果输出
所有结果文件将保存在 `result/{network_name}/` 目录下

## 性能优化效果

1. **内存使用**: 通过及时清理缓存，减少内存占用
2. **执行效率**: 优化的垃圾回收策略提高程序稳定性
3. **可维护性**: 清晰的代码结构便于后续维护和扩展
4. **监控能力**: 实时的性能监控帮助识别瓶颈

## 注意事项

1. **依赖要求**: 需要安装 `psutil` 库用于内存监控
2. **兼容性**: 保持了原有算法逻辑的完整性
3. **扩展性**: 新的结构便于添加更多监控和优化功能
